# RAG Chatbot

A professional AI assistant powered by Retrieval-Augmented Generation (RAG) technology, built with FastAPI, LangChain, and ChromaDB.

## Overview

This RAG chatbot serves as an intelligent assistant that can answer questions based on your custom knowledge base. It combines the power of large language models with document retrieval to provide accurate, contextual responses grounded in your specific documents.

## Features

- **FastAPI Backend**: High-performance API with automatic documentation
- **RAG Architecture**: Retrieval-Augmented Generation for accurate, context-aware responses
- **Vector Database**: ChromaDB for efficient document storage and similarity search
- **Session Management**: Maintains conversation history across chat sessions
- **Document Processing**: Supports Markdown and text file ingestion
- **CORS Support**: Ready for frontend integration
- **Health Monitoring**: Built-in health check endpoint

## Architecture

- **LLM**: Groq-powered language model for response generation
- **Embeddings**: HuggingFace sentence transformers for document vectorization
- **Vector Store**: ChromaDB for persistent vector storage
- **Framework**: FastAPI for REST API endpoints
- **Document Processing**: Lang<PERSON>hain for document loading and text splitting

## Prerequisites

- Python 3.12 or higher
- UV package manager (recommended) or pip

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd RAG-CHATBOT
   ```

2. **Install dependencies**:
   ```bash
   # Using UV (recommended)
   uv sync

   # Or using pip
   pip install -e .
   ```

3. **Set up environment variables**:
   Create a `.env` file in the root directory and add your API keys:
   ```env
   GROQ_API_KEY=your_groq_api_key_here
   ```

## Usage

### 1. Load Documents

First, add your documents to the `knowledge_base/docs/` directory (supports `.md` and `.txt` files), then run:

```bash
python scripts/load_documents.py
```

This will process your documents and store them in the ChromaDB vector database.

### 2. Start the API Server

```bash
# Using UV
uv run python main.py

# Or using Python directly
python main.py
```

The API will be available at `http://localhost:8000`

### 3. API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation (Swagger UI).

## API Endpoints

### POST /chat
Send a message to the chatbot.

**Request Body**:
```json
{
  "query": "Your question here",
  "session_id": "unique-session-identifier"
}
```

**Response**:
```json
{
  "response": "AI assistant's response"
}
```

### GET /health
Health check endpoint.

**Response**:
```json
{
  "status": "ok"
}
```

## Project Structure

```
RAG-CHATBOT/
├── agents/                 # Core AI agent modules
│   ├── chat_bot.py        # Main chatbot logic and agent chain
│   ├── embeddings.py      # Embedding model configuration
│   ├── llm_setup.py       # Language model setup
│   ├── prompt_templates.py # Chat prompt templates
│   └── vector_db.py       # Vector database operations
├── chroma_db/             # ChromaDB storage (auto-generated)
├── knowledge_base/        # Document storage
│   └── docs/              # Place your .md and .txt files here
├── scripts/               # Utility scripts
│   └── load_documents.py  # Document ingestion script
├── main.py               # FastAPI application entry point
├── pyproject.toml        # Project configuration and dependencies
└── README.md            # This file
```

## Configuration

The chatbot can be customized by modifying:

- **Prompt Templates**: Edit `agents/prompt_templates.py` to change the assistant's behavior
- **LLM Settings**: Modify `agents/llm_setup.py` to use different models or parameters
- **Vector Store**: Adjust `agents/vector_db.py` for different embedding models or chunk sizes

## Development

### Adding New Documents

1. Place your `.md` or `.txt` files in `knowledge_base/docs/`
2. Run the document loader: `python scripts/load_documents.py`
3. The documents will be automatically processed and added to the vector database

### Customizing the Assistant

- **Personality**: Modify the system prompt in `agents/prompt_templates.py`
- **Response Style**: Adjust the prompt templates and fallback responses
- **Knowledge Scope**: Update the out-of-knowledge response in `agents/chat_bot.py`

## Dependencies

Key dependencies include:

- **FastAPI**: Web framework for the API
- **LangChain**: Framework for building LLM applications
- **ChromaDB**: Vector database for document storage
- **Groq**: LLM API provider
- **HuggingFace**: Embedding models
- **Sentence Transformers**: Text embedding generation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For questions or issues, please [create an issue](link-to-issues) or contact the development team.