[project]
name = "rag-chatbot"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]>=0.116.1",
    "langchain>=0.2.11", # Added explicitly
    "chromadb>=1.0.0", # Added explicitly (though pulled by langchain-chroma)
    "langchain-chroma>=0.2.5",
    "langchain-huggingface>=0.3.1",
    "sentence-transformers>=5.0.0",
    "torch>=2.7.1",
    "langchain-community>=0.3.27",
    "langchain-groq>=0.3.6",
    "unstructured>=0.4.16",
    "markdown>=3.8.2",
    "groq>=0.30.0",
]
