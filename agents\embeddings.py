# core/embeddings.py
# Initializes and provides the Hugging Face Sentence Transformer embedding model

from langchain_huggingface.embeddings import HuggingFaceEmbeddings

def get_embedding_model():
    """
    Initializes and returns a Hugging Face Sentence Transformer embedding model
    for use in RAG systems or any other vector-based retrieval pipeline.
    """
    model_name = "sentence-transformers/all-MiniLM-L6-v2"
    
    # Set model device: use 'cuda' for GPU, 'cpu' for CPU
    model_kwargs = {
        'device': 'cpu'
    }
    
    # Optional encoding settings
    encode_kwargs = {
        'normalize_embeddings': False  # Keeping raw vector space
    }

    # Initialize and return embedding model
    return HuggingFaceEmbeddings(
        model_name=model_name,
        model_kwargs=model_kwargs,
        encode_kwargs=encode_kwargs
    )
