# scripts/ingest_docs_to_chroma.py
# Script to process .txt and .md files from a folder and load into ChromaDB using LangChain

import os
import sys

# Add the parent directory to allow importing from 'core'
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from langchain_community.document_loaders import TextLoader, UnstructuredMarkdownLoader
from agents.vector_db import get_vector_store, split_documents

DOCS_PATH = "./knowledge_base/docs"

def load_documents():
    """
    Loads .txt and .md documents from the DOCS_PATH directory.
    """
    documents = []
    for filename in os.listdir(DOCS_PATH):
        filepath = os.path.join(DOCS_PATH, filename)
        if os.path.isfile(filepath):
            if filename.endswith(".txt"):
                loader = TextLoader(filepath)
            elif filename.endswith(".md"):
                loader = UnstructuredMarkdownLoader(filepath)
            else:
                print(f"Skipping unsupported file: {filename}")
                continue
            documents.extend(loader.load())
    return documents

if __name__ == "__main__":
    print("🔁 Starting document ingestion...")

    print("📄 Loading documents...")
    documents = load_documents()
    print(f"✅ Loaded {len(documents)} documents.")

    print("✂️ Splitting documents into chunks...")
    splits = split_documents(documents)
    print(f"✅ Split into {len(splits)} chunks.")

    print("📦 Loading chunks into ChromaDB...")
    vector_store = get_vector_store()
    vector_store.add_documents(splits)

    print("✅ Ingestion complete!")
