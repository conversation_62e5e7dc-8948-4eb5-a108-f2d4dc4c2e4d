# core/agent.py
# Contains the main Agent (ABCBot) logic
from operator import itemgetter

from langchain_core.runnables import (
    RunnablePassthrough,
    RunnableWithMessageHistory,
    RunnableLambda,
    RunnableBranch,
)
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory

from agents.llm_setup import get_llm
from agents.vector_db import get_vector_store, get_retriever
from agents.prompt_templates import human_prompt

# In-memory session store
store = {}

# Fallback response when no documents are relevant
OUT_OF_KNOWLEDGE_RESPONSE = (
    "I apologize, but I don't have that specific information in my knowledge base at the moment. "
    "However, I can connect you directly with a human expert from our team who can assist you further. "
    "Would you like their contact details or for them to reach out to you?"
)

def get_session_history(session_id: str) -> BaseChatMessageHistory:
    """
    Retrieves or initializes chat history for a given session ID.
    """
    if session_id not in store:
        store[session_id] = ChatMessageHistory()
    return store[session_id]

def get_agent_chain():
    """
    Initializes and returns the main RAG agent chain with memory, retrieval, and fallback logic.
    """
    llm = get_llm()
    vector_store = get_vector_store()
    retriever = get_retriever(vector_store)

    # Determine if retrieved context is empty
    def is_context_empty(input_dict):
        context = input_dict.get("context")
        return not context or all(not doc.page_content.strip() for doc in context)

    # Branch to fallback response when no relevant context is found
    out_of_knowledge_branch = RunnableLambda(lambda x: OUT_OF_KNOWLEDGE_RESPONSE)

    # Branch to generate response using prompt + LLM + output parser
    rag_processing_branch = (
        human_prompt
        | llm
        | StrOutputParser()
    )

    # Combine both branches using conditional logic
    full_rag_chain = (
        {
            "context": itemgetter("input") | retriever,
            "chat_history": itemgetter("chat_history"),
            "input": itemgetter("input"),
        }
        | RunnableBranch(
            (is_context_empty, out_of_knowledge_branch),
            rag_processing_branch,
        )
    )

    # Add session-based message history for conversational context
    with_message_history = RunnableWithMessageHistory(
        full_rag_chain,
        get_session_history,
        input_messages_key="input",
        history_messages_key="chat_history",
    )

    return with_message_history
